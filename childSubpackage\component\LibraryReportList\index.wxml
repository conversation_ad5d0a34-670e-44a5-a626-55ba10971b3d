<!-- 撼地智库完整页面组件 -->
<view class="library-report-list">
  <!-- 列表页面 -->
  <view class="list-container">
    <view class="company_num"> </view>

    <!-- VIP用户：显示完整列表 -->
    <view wx:if="{{isVip}}" class="vip-container">
      <view class="card-box" style="height: {{containerHeight}}px;">
        <refresh-scroll
          id="libraryRefreshScroll"
          container-height="{{containerHeight}}"
          request-url="{{RequestUrlFn}}"
          requestParams="{{requestParams}}"
          empty-text="暂无研报数据"
          empty-tip=""
          bind:datachange="onListDataChange"
          bind:error="onError"
          custom-wrapper-class="custom-wrapper-class"
          requestType="pageIndex"
        >
          <view slot="content" class="list_wrp">
            <ReportCard
              report-list="{{reportList}}"
              bindreportclick="onReportClick"
            />
          </view>
        </refresh-scroll>
      </view>
    </view>

    <!-- 非VIP用户：显示一条数据 + VIP页面 -->
    <scroll-view
      wx:else
      class="non-vip-container"
      scroll-y="{{true}}"
      style="height: {{containerHeight}}px;"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <!-- 非VIP用户在报告tab中的占位空间 -->
      <view
        wx:if="{{isReportTab && !isVip}}"
        style="height: 20rpx;background:transparent;"
      ></view>
      <!-- 显示第一条研报数据 -->
      <view wx:if="{{reportList.length > 0}}" class="first-report-container">
        <SingleReportCard
          report-data="{{reportList[0]}}"
          bindreportclick="onReportClick"
        />
      </view>
      <!-- 剩余信息提示 - 仅在ResearchThreeList页面显示 -->
      <view wx:if="{{showVipPage && showRemainingInfo}}" class="remaining-info">
        <view class="remaining-text"
          >剩余<text>{{totalCount > 1 ? totalCount - 1 : 0}}</text
          >条相关信息</view
        >
      </view>
      <!-- VIP购买页面 -->
      <vip-page wx:if="{{showVipPage}}" bind:paySuccess="onVipPaySuccess">
      </vip-page>
      <view style="height: 60rpx;"></view>
    </scroll-view>
  </view>
</view>
